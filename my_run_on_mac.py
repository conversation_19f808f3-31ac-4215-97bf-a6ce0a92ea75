from verl import DataProto
class Config():
    def __init__(self):
        self.train_files='/Users/<USER>/ai_data/countdown/train.parquet'
        self.val_files='/Users/<USER>/ai_data/countdown/test.parquet'
        self.base_model='/Users/<USER>/.cache/modelscope/hub/models/qwen/Qwen2.5-0.5B'
        self.prompt_key='prompt'
        self.max_prompt_length=512
        self.train_batch_size=1024
        self.return_raw_chat=False
        self.total_epochs=10
        self.total_training_steps=None

        self.val_batch_size=16


class MyClass():
    def __init__(self):
        from verl.utils.fs import copy_local_path_from_hdfs
        local_path = copy_local_path_from_hdfs(CONFIG.base_model)
            # instantiate tokenizer
        from verl.utils import hf_tokenizer
        self.tokenizer = hf_tokenizer(local_path)


    def _create_dataloader(self):
        from torch.utils.data import DataLoader
        # TODO: we have to make sure the batch size is divisible by the dp size
        from verl.utils.dataset.rl_dataset import RLHFDataset, collate_fn
        self.train_dataset = RLHFDataset(parquet_files=CONFIG.train_files,
                                         tokenizer=self.tokenizer,
                                         prompt_key=CONFIG.prompt_key,
                                         max_prompt_length=CONFIG.max_prompt_length,
                                         filter_prompts=True,
                                         return_raw_chat=CONFIG.return_raw_chat,
                                         truncation='error')
        self.train_dataloader = DataLoader(dataset=self.train_dataset,
                                           batch_size=CONFIG.train_batch_size,
                                           shuffle=True,
                                           drop_last=True,
                                           collate_fn=collate_fn)

        self.val_dataset = RLHFDataset(parquet_files=CONFIG.val_files,
                                       tokenizer=self.tokenizer,
                                       prompt_key=CONFIG.prompt_key,
                                       max_prompt_length=CONFIG.max_prompt_length,
                                       filter_prompts=True,
                                       return_raw_chat=CONFIG.return_raw_chat,
                                       truncation='error')
        self.val_dataloader = DataLoader(dataset=self.val_dataset,
                                         batch_size=len(self.val_dataset),
                                         shuffle=True,
                                         drop_last=True,
                                         collate_fn=collate_fn)

        assert len(self.train_dataloader) >= 1
        assert len(self.val_dataloader) >= 1

        print(f'Size of train dataloader: {len(self.train_dataloader)}')
        print(f'Size of val dataloader: {len(self.val_dataloader)}')

        # inject total_training_steps to actor/critic optim_config. This is hacky.
        total_training_steps = len(self.train_dataloader) * CONFIG.total_epochs

        if CONFIG.total_training_steps is not None:
            total_training_steps = CONFIG.total_training_steps

        self.total_training_steps = total_training_steps
        print(f'Total training steps: {self.total_training_steps}')


    def main(self):
        self._create_dataloader()
        self.fit()
    def fit(self):
        for epoch  in range(10):
            print(f"Epoch {epoch}")
            for batch_dict in self.train_dataloader:
                metrics = {}
                timing_raw = {}

                batch: DataProto = DataProto.from_single_dict(batch_dict)

                # pop those keys for generation
                gen_batch = batch.pop(batch_keys=['input_ids', 'attention_mask', 'position_ids'])


                pass
        pass

if __name__ == "__main__":
    CONFIG=Config()
    my=MyClass()
    my.main()
    print("done")